{"name": "pointcloud", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src", "deploy": "./scripts/upload-dist.sh /mnt/d/pointclude/TankCapacity/x64/Release ************* root"}, "dependencies": {"@antv/g": "^6.1.25", "@antv/g-canvas": "^2.0.44", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@unocss/reset": "^65.4.2", "@vue/eslint-config-prettier": "^10.1.0", "element-plus": "^2.9.3", "eslint-config-prettier": "^10.0.1", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "pinia": "^3.0.2", "sass": "^1.83.4", "sortablejs": "^1.15.6", "three": "0.159.0", "unocss": "^65.4.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@unocss/preset-uno": "^65.4.2", "@unocss/transformer-directives": "^65.4.2", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.5", "vite-plugin-svg-icons": "^2.0.1"}}