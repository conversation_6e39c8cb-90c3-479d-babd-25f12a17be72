<template>
  <div class="h-full flex flex-col space-y-4 p-4 box-border">
    <!-- 顶部输入区域 -->
    <el-form :model="form" class="bg-white p-16px rounded-8px shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)]" :disabled="!permission[0]">
      <el-checkbox v-model="patchPara.auto.check" label="自动补点" class="mb-2" :true-label="true" :false-label="false" />
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="精度">
            <NumberField v-model="patchPara.auto.tol" placeholder="请输入精度" clearable :disabled="!patchPara.auto.check">
              <template #append>mm</template>
            </NumberField>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="宽度">
            <NumberField v-model="patchPara.auto.width" placeholder="请输入宽度" clearable :disabled="!patchPara.auto.check">
              <template #append>mm</template>
            </NumberField>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item></el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 表格区域 -->
    <div class="flex flex-1 bg-white shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] rounded-8px p-16px box-border overflow-hidden">
      <div class="flex flex-col w-800px mr-4">
        <el-checkbox v-model="patchPara.corrugate.check" :disabled="!permission[0]" label="槽型舱壁" :true-label="true" :false-label="false" />
        <div class="flex-1 bg-gray-100 border border-gray-300 rounded-md mt-2 p-2 flex items-center justify-center">
          <img g src="@/assets/slot.png" alt="槽型舱壁示意图" class="object-cover" />
          <div class="槽型舱壁示意图区域"></div>
        </div>
      </div>
      <div class="flex-1 overflow-hidden">
        <BaseTable v-model:data="patchPara.corrugate.data" :columns="columns" :loading="loading" row-key="id" height="100%" can-add :disabled="!permission[0] || !patchPara.corrugate.check"></BaseTable>
      </div>
    </div>

    <!-- 自定义区域 -->
    <div class="flex-1 bg-white shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] rounded-8px p-16px box-border flex flex-col">
      <el-checkbox v-model="patchPara.custom.check" :disabled="!permission[0]" label="自定义" :true-label="true" :false-label="false" />
      <div class="mt-4 flex-1 overflow-hidden">
        <BaseTable v-model:data="patchPara.custom.data" :columns="customColumns" height="100%" can-add :disabled="!permission[0] || !patchPara.custom.check"></BaseTable>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, h, unref } from "vue"
import { onBeforeRouteLeave } from "vue-router"
import BaseTable from "@/components/baseTable/index.vue"
import { useStore } from "@/store"
import { storeToRefs } from "pinia"
import { isEqual, deepClone } from "@/common/js/dataUtils"
import { useParameterWatchers } from "@/common/js/useWatchers"
import NumberField from "@/components/numberField/index.vue"
const store = useStore()
const { patchPara, permission } = storeToRefs(store)

const loading = ref(false)
const form = ref({
  precision: "",
  width: "",
  autoFill: false,
  grooveWall: false,
  customEnabled: false,
})

/**
 * 检查槽型舱壁数据项是否有效
 * @param {Object} item - 槽型舱壁数据项
 * @returns {boolean} - 是否有效
 */
const isValidCorrugateItem = (item) => {
  return ["dir", "type", "width", "webwidth", "depth"].every((key) => item[key] !== undefined && item[key] !== "")
}

/**
 * 检查自定义数据项是否有效
 * @param {Object} item - 自定义数据项
 * @returns {boolean} - 是否有效
 */
const isValidCustomItem = (item) => {
  // 检查所有必需字段是否存在且不为空
  const requiredFields = ["dir", "x1", "y1", "z1", "x2", "y2", "z2"]
  return requiredFields.every((key) => {
    const value = item[key]
    return value !== undefined && value !== "" && value !== null
  })
}

/**
 * 转换数据中的点格式（用于提交）
 * @param {Object} data - 原始数据
 * @returns {Object} - 转换后的数据
 */
const convertPointsForSubmit = (data) => {
  const convertedData = deepClone(data)

  return convertedData
}

/**
 * 过滤并清理数据
 * @param {Object} data - 原始数据
 * @returns {Object} - 过滤后的数据
 */
const filterAndCleanData = (data) => {
  // 深拷贝数据，避免直接修改响应式对象
  const cleanData = deepClone(data)

  // 过滤槽型舱壁数据
  if (cleanData.corrugate && Array.isArray(cleanData.corrugate.data)) {
    cleanData.corrugate.data = cleanData.corrugate.data.filter(isValidCorrugateItem)
  }

  // 过滤自定义数据
  if (cleanData.custom && Array.isArray(cleanData.custom.data)) {
    cleanData.custom.data = cleanData.custom.data.filter(isValidCustomItem)
  }

  return cleanData
}

/**
 * 更新补点参数
 * @param {Object} data - 要更新的数据
 */
const updatePatchPara = (data) => {
  // 先过滤清理数据
  const cleanedData = filterAndCleanData(data)

  // 比较数据是否有变化
  if (!isEqual(cleanedData, lastSubmittedData.value)) {
    // 数据有变化，调用接口
    store.updatePatchPara({
      tankname: store.currentTank.tankname,
      para: cleanedData,
    })

    // 更新上次提交的数据
    lastSubmittedData.value = cleanedData
    console.log("数据已更新")
  } else {
    console.log("数据未变化，不更新")
  }
}

const dirOptions = [
  { value: 0, label: "X-" },
  { value: 1, label: "X+" },
  { value: 2, label: "Y-" },
  { value: 3, label: "Y+" },
]

const typeOptions = [
  { value: 0, label: "垂直槽型" },
  { value: 1, label: "水平槽型" },
]

const customDirOption = [
  { value: 0, label: "X" },
  { value: 1, label: "Y" },
  { value: 2, label: "Z" },
]

// 表格列配置
const columns = [
  {
    prop: "dir",
    label: "舱壁位置",
    render({ value }) {
      const option = dirOptions.find((option) => option.value == value)
      return h("div", {}, option?.label)
    },
    editor: {
      ctype: "select",
      options: dirOptions,
    },
  },
  {
    prop: "type",
    label: "槽型",
    render({ value }) {
      const option = typeOptions.find((option) => option.value == value)
      return h("div", {}, option?.label)
    },
    editor: {
      ctype: "select",
      options: dirOptions,
    },
    editor: {
      ctype: "select",
      options: typeOptions,
    },
  },
  { prop: "width", label: "槽型面板宽度a(mm)", editor: { ctype: "number" } },
  { prop: "webwidth", label: "槽型腹板宽度b(mm)", editor: { ctype: "number" } },
  { prop: "depth", label: "槽深c(mm)", editor: { ctype: "number" } },
]

// 自定义区域表格列配置
const customColumns = [
  {
    prop: "dir",
    label: "拉伸方向",
    render({ value }) {
      const option = customDirOption.find((option) => option.value == value)
      return h("div", {}, option?.label)
    },
    editor: {
      ctype: "select",
      options: customDirOption,
    },
  },
  {
    prop: "x1",
    label: "x1(mm)",

    editor: {
      ctype: "number",
    },
  },
  {
    prop: "y1",
    label: "y1(mm)",

    editor: {
      ctype: "number",
    },
  },
  {
    prop: "z1",
    label: "z1(mm)",

    editor: {
      ctype: "number",
    },
  },
  {
    prop: "x2",
    label: "x2(mm)",

    editor: {
      ctype: "number",
    },
  },
  {
    prop: "y2",
    label: "y2(mm)",

    editor: {
      ctype: "number",
    },
  },
  {
    prop: "z2",
    label: "z2(mm)",

    editor: {
      ctype: "number",
    },
  },
]

const { lastSubmittedData } = useParameterWatchers({
  watchData: () => patchPara.value,
  updateCallback: updatePatchPara,
  debounceDelay: 500,
  getCurrentTank: () => unref(store.currentTank),
  loadData: () => {
    return store.loadPatchPara({
      tankname: unref(store.currentTank).tankname,
    })
  },
  getData: () => deepClone(unref(store.patchPara)),
})

onBeforeRouteLeave(() => {
  store.calTankPatch({
    tankname: unref(store.currentTank).tankname,
  })
})
</script>
