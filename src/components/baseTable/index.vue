<template>
  <div class="base-table-wrapper">
    <!-- 表格区域 -->
    <div class="table-area">
      <el-table ref="tableRef" row-key="id" size="small" v-bind="$attrs" :data="syncData" :border="true" height="100%" :row-class-name="tableRowClassName" @row-contextmenu="handleContextMenu" @row-click="rowClick">
        <!-- 通过配置生成列 -->
        <template v-if="columns">
          <template v-for="(col, colIndex) in columns" :key="colIndex">
            <!-- Selection类型列 -->
            <el-table-column v-if="col.type === 'selection'" v-bind="col" />
            <!-- 其他类型列 -->
            <el-table-column v-else v-bind="filterCol(col)">
              <template #header="{ column }">
                <component :is="col.renderHeader" />
                <div>{{ column.label }}</div>
              </template>
              <!-- 默认单元格模板 -->
              <template #default="scope">
                <div
                  class="custom-cell"
                  :class="getCellClass(scope.$index, colIndex)"
                  :tabindex="canAdd ? 0 : undefined"
                  @click="handleCellClick(scope.$index, colIndex)"
                  @keydown="handleKeydown($event, scope.$index, colIndex)"
                  @dblclick="handleDblclick($event, scope.$index, colIndex)"
                  @focus="handleCellFocus(scope.$index, colIndex)"
                  @blur="handleCellBlur(scope.$index, colIndex)">
                  <Editor v-if="isActiveCellAt(scope.$index, colIndex)" ref="inputRef" v-model="scope.row[col.prop]" :data="scope.row" :column="col" @blur="handleBlur" @keydown.enter="handleBlur" @change="handleChange" />
                  <template v-else-if="col.render">
                    <component :is="col.render" v-bind="scope" :row="scope.row" :value="scope.row[col.prop]" :column="scope.column" :index="scope.$index" />
                  </template>
                  <template v-else>{{ scope.row[col.prop] }}</template>
                </div>
              </template>
            </el-table-column>
          </template>
        </template>
        <!-- 默认插槽 -->
        <slot v-else></slot>
      </el-table>
    </div>
  </div>
  <!-- 右键菜单 Popover -->
  <el-popover ref="contextMenuRef" v-model:visible="contextMenuVisible" popper-style="padding:0" trigger="manual" :virtual-ref="virtualRef" virtual-triggering :show-arrow="false" placement="bottom-start" @mousedown.prevent>
    <div class="context-menu bg-white rounded-md shadow-lg border border-solid border-gray-200 py-1">
      <div v-if="!isRowContextMenu && !canEmptyRow" class="menu-item" @mousedown.prevent.stop="menuItemClick('add')">
        <i class="fa-solid fa-plus text-blue-500"></i>
        <span>添加行</span>
      </div>
      <template v-if="isRowContextMenu">
        <div v-if="shouldShowMenuItem('insertUp')" class="menu-item" @mousedown.prevent.stop="handleInsertRow('up')">
          <i class="fa-solid fa-arrow-up text-green-500"></i>
          <span>向上插入</span>
        </div>
        <div v-if="shouldShowMenuItem('insertDown')" class="menu-item" @mousedown.prevent.stop="handleInsertRow('down')">
          <i class="fa-solid fa-arrow-down text-green-500"></i>
          <span>向下插入</span>
        </div>
        <div v-if="shouldShowMenuItem('delete')" class="menu-item" @click="handleDeleteRow">
          <i class="fa-solid fa-trash text-red-500"></i>
          <span>删除行</span>
        </div>
      </template>
    </div>
  </el-popover>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, watchEffect, unref } from "vue"
import { ElMessageBox } from "element-plus"
import Editor from "./editor.vue"
import { EMPTY_ROW_SYMBOL, EMPTY_ROW } from "./constant"

// Props 定义
const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  canAdd: {
    type: Boolean,
    default: false,
  },
  contextMenuEnabled: {
    type: Boolean,
    default: true,
  },
  highlight: {
    type: Boolean,
    default: false,
  },
  initRow: {
    type: Function,
    default() {
      return {}
    },
  },
  canEmptyRow: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  customMenuControl: {
    type: Function,
    default: null,
  },
})

const filterCol = (col) => {
  return {
    ...col,
    renderHeader: undefined,
  }
}

const emit = defineEmits(["update:data", "rowClick", "menuClick", "rowInputChange", "blur", "duplicateError"])

const rowIndex = defineModel("rowIndex", {
  type: Number,
  default: -1,
})

// 添加单元格索引状态
const cellIndex = defineModel("cellIndex", {
  type: Array,
  default: () => [-1, -1], // [rowIndex, colIndex]
})
// 表格状态管理
const useTableState = () => {
  const tableRef = ref()
  const inputRef = ref(null)
  const syncData = ref(props.data)

  watchEffect(() => {
    syncData.value = props.canAdd && props.canEmptyRow && !props.disabled ? [...props.data, EMPTY_ROW] : props.data
  })

  return { tableRef, inputRef, syncData }
}

// 重复检测工具函数
const useDuplicateCheck = () => {
  // 检测指定列是否有重复数据
  const checkDuplicate = (data, columnProp, currentRowIndex, currentValue) => {
    if (currentValue === undefined || currentValue === null || currentValue === "") {
      return false
    }

    return data.some((row, index) => {
      // 跳过当前行和空行
      if (index === currentRowIndex || row.hasOwnProperty(EMPTY_ROW_SYMBOL)) {
        return false
      }
      return row[columnProp] === currentValue
    })
  }

  return { checkDuplicate }
}

// 单元格编辑管理
const useTableEdit = ({ tableState, rowOperations }) => {
  const activeCell = ref([-1, -1]) // 编辑状态的单元格
  const selectedCell = ref([-1, -1]) // 选中/焦点状态的单元格（合并）
  const { checkDuplicate } = useDuplicateCheck()
  const isShowingDuplicateDialog = ref(false) // 防止重复弹框

  const isActiveCellAt = (rowIndex, colIndex) => {
    return activeCell.value[0] === rowIndex && activeCell.value[1] === colIndex
  }

  const isSelectedCellAt = (rowIndex, colIndex) => {
    return selectedCell.value[0] === rowIndex && selectedCell.value[1] === colIndex
  }

  const handleKeydown = (e, rowIndex, colIndex) => {
    const column = props.columns[colIndex]

    if (e.target.classList.contains("custom-cell")) {
      if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "Tab"].includes(e.key)) {
        e.preventDefault()
        // 使用实际渲染的数据长度来计算最大行数
        const maxRow = unref(tableState.syncData).length - 1

        // 获取所有可导航列的原始索引
        const navigableColIndexes = []
        props.columns.forEach((col, index) => {
          if (col.type !== "selection") {
            navigableColIndexes.push(index)
          }
        })

        // 找到当前列在可导航列中的位置
        const currentNavIndex = navigableColIndexes.indexOf(colIndex)
        const maxNavIndex = navigableColIndexes.length - 1

        let [newRow, newNavIndex] = [rowIndex, currentNavIndex]

        switch (e.key) {
          case "ArrowUp":
            newRow = Math.max(0, rowIndex - 1)
            break
          case "ArrowDown":
            newRow = Math.min(maxRow, rowIndex + 1)
            break
          case "ArrowLeft":
            newNavIndex = Math.max(0, currentNavIndex - 1)
            break
          case "ArrowRight":
            newNavIndex = Math.min(maxNavIndex, currentNavIndex + 1)
            break
          case "Tab":
            if (e.shiftKey) {
              // Shift+Tab: 向前移动
              if (currentNavIndex > 0) {
                newNavIndex = currentNavIndex - 1
              } else if (rowIndex > 0) {
                newRow = rowIndex - 1
                newNavIndex = maxNavIndex
              }
            } else {
              // Tab: 向后移动
              if (currentNavIndex < maxNavIndex) {
                newNavIndex = currentNavIndex + 1
              } else if (rowIndex < maxRow) {
                newRow = rowIndex + 1
                newNavIndex = 0
              }
            }
            break
        }

        // 获取目标列的原始索引
        const newColIndex = navigableColIndexes[newNavIndex]

        // 直接查找目标单元格
        const tableBody = tableRef.value.$el.querySelector(".el-table__body tbody")
        if (tableBody) {
          const rows = tableBody.querySelectorAll("tr")
          if (rows[newRow]) {
            // 通过原始列索引查找对应的自定义单元格
            const allCells = rows[newRow].querySelectorAll("td")
            if (allCells[newColIndex]) {
              const targetCell = allCells[newColIndex].querySelector(".custom-cell")
              if (targetCell) {
                targetCell.focus()
              }
            }
          }
        }
      } else if (e.key === " " || e.key === "Spacebar") {
        // 空格键设置选中状态
        e.preventDefault()
        selectedCell.value = [rowIndex, colIndex]
        cellIndex.value = [rowIndex, colIndex]
      } else {
        // 只有可编辑的列才能进入编辑状态
        if (!column.editor) {
          return
        }

        if (unref(tableState.syncData)[rowIndex].hasOwnProperty(EMPTY_ROW_SYMBOL)) {
          rowOperations.handleAddRow()
        }
        // 进入编辑状态
        activeCell.value = [rowIndex, colIndex]
        // 编辑状态下保持焦点和选中状态不变
        cellIndex.value = [rowIndex, colIndex]

        nextTick(() => {
          e.target.querySelector("input")?.select()
          e.target.querySelector("input")?.focus()
        })
      }
    } else {
      if (props.disabled) {
        return
      }
      if (e.key === "Enter") {
        // 按 Enter 键时也需要检查重复
        e.preventDefault()
        handleBlur()
      }
    }
  }

  const handleDblclick = (e, rowIndex, colIndex) => {
    const column = props.columns[colIndex]
    if (!column.editor || props.disabled) {
      return
    }
    if (unref(tableState.syncData)[rowIndex].hasOwnProperty(EMPTY_ROW_SYMBOL)) {
      rowOperations.handleAddRow()
    }

    // 进入编辑状态
    activeCell.value = [rowIndex, colIndex]
    // 编辑状态下保持焦点和选中状态不变
    cellIndex.value = [rowIndex, colIndex]

    const parentNode = e.target.parentNode
    nextTick(() => {
      parentNode.querySelector("input")?.select()
      parentNode.querySelector("input")?.focus()
    })
  }

  const handleBlur = () => {
    // 失去焦点时退出编辑状态，但保持单元格选中状态
    const [currentRow, currentCol] = activeCell.value

    // 检查重复数据
    if (currentRow !== -1 && currentCol !== -1) {
      const column = props.columns[currentCol]
      const currentData = unref(tableState.syncData)
      const currentValue = currentData[currentRow][column.prop]

      // 如果列配置了重复检测
      if (column.editor?.checkDuplicate && checkDuplicate(props.data, column.prop, currentRow, currentValue)) {
        // 防止重复弹框
        if (isShowingDuplicateDialog.value) {
          return
        }

        isShowingDuplicateDialog.value = true

        // 显示弹框提示
        ElMessageBox.alert(`${column.label || column.prop} 的值 "${currentValue}" 已存在，请输入不同的值`, "重复数据提示", {
          confirmButtonText: "确定",
          type: "warning",
        })
          .then(() => {
            // 用户点击确定后，重新聚焦到编辑器
            nextTick(() => {
              const tableBody = tableRef.value.$el.querySelector(".el-table__body tbody")
              if (tableBody) {
                const rows = tableBody.querySelectorAll("tr")
                if (rows[currentRow]) {
                  const allCells = rows[currentRow].querySelectorAll("td")
                  if (allCells[currentCol]) {
                    const targetCell = allCells[currentCol].querySelector(".custom-cell")
                    if (targetCell) {
                      targetCell.focus()
                      // 重新进入编辑状态
                      activeCell.value = [currentRow, currentCol]
                      nextTick(() => {
                        const input = targetCell.querySelector("input")
                        if (input) {
                          input.select()
                          input.focus()
                        }
                      })
                    }
                  }
                }
              }
            })
          })
          .finally(() => {
            // 无论用户点击确定还是关闭，都重置标志
            isShowingDuplicateDialog.value = false
          })
        return // 不退出编辑状态
      }
    }

    activeCell.value = [-1, -1] // 退出编辑状态

    const rowData = unref(tableState.syncData)[currentRow]
    emit("blur", { row: rowData })
    // 如果之前有编辑的单元格，将其设为选中状态
    if (currentRow !== -1 && currentCol !== -1) {
      // selectedCell.value = [currentRow, currentCol]
      cellIndex.value = [currentRow, currentCol]
    }
  }

  // 获取单元格类名
  const getCellClass = (rowIdx, colIdx) => {
    const classes = {}

    // 编辑状态 - 最高优先级，覆盖所有其他状态
    if (activeCell.value[0] === rowIdx && activeCell.value[1] === colIdx) {
      classes["active-cell"] = true
    } else if (selectedCell.value[0] === rowIdx && selectedCell.value[1] === colIdx) {
      // 选中/焦点状态
      classes["selected-cell"] = true
    }

    return classes
  }

  // 处理单元格focus事件 - 管理选中/焦点状态
  const handleCellFocus = (rowIdx, colIdx) => {
    cellIndex.value = [rowIdx, colIdx]

    // 如果不是编辑状态，则设置选中状态
    if (activeCell.value[0] !== rowIdx || activeCell.value[1] !== colIdx) {
      selectedCell.value = [rowIdx, colIdx]
    }
  }

  // 处理单元格blur事件
  const handleCellBlur = (rowIdx, colIdx) => {
    // 延迟清除选中状态，防止与click事件冲突
  }

  // 处理单元格点击 - 管理选中状态
  const handleCellClick = (rowIdx, colIdx) => {
    // 点击时设置选中状态（编辑状态除外）
    if (activeCell.value[0] !== rowIdx || activeCell.value[1] !== colIdx) {
      selectedCell.value = [rowIdx, colIdx]
    }
    cellIndex.value = [rowIdx, colIdx]
  }

  return {
    activeCell,
    selectedCell,
    isActiveCellAt,
    isSelectedCellAt,
    handleKeydown,
    handleDblclick,
    handleBlur,
    getCellClass,
    handleCellClick,
    handleCellFocus,
    handleCellBlur,
  }
}

// 右键菜单管理
const useContextMenu = (tableRef) => {
  const contextMenuVisible = ref(false)
  const virtualRef = ref(null)
  const contextMenuRowIndex = ref(-1)
  const isRowContextMenu = ref(false)

  // 控制菜单项显示的方法
  const shouldShowMenuItem = (menuType) => {
    if (!props.customMenuControl) {
      return true
    }

    const rowData = contextMenuRowIndex.value >= 0 ? props.data[contextMenuRowIndex.value] : null
    return props.customMenuControl(menuType, rowData, contextMenuRowIndex.value)
  }

  const handleContextMenu = async (row, column, event) => {
    if (!props.contextMenuEnabled) return

    event.preventDefault()
    event.stopPropagation()
    // 判断是否在行上右键
    isRowContextMenu.value = row !== null
    contextMenuRowIndex.value = row ? props.data.indexOf(row) : -1

    if (!unref(isRowContextMenu) && props.canEmptyRow) {
      return
    }

    // 设置虚拟引用元素位置为鼠标点击位置
    virtualRef.value = {
      getBoundingClientRect: () => ({
        width: 0,
        height: 0,
        left: event.clientX + 5,
        right: event.clientX,
        top: event.clientY - 10,
        bottom: event.clientY,
      }),
    }

    await nextTick()
    contextMenuVisible.value = true
  }

  const closeContextMenu = () => {
    contextMenuVisible.value = false
  }

  const handleClickOutside = () => {
    if (contextMenuVisible.value) {
      contextMenuVisible.value = false
    }
  }

  // 事件监听器
  onMounted(() => {
    document.addEventListener("click", handleClickOutside)
    document.addEventListener(
      "contextmenu",
      (e) => {
        if (!tableRef.value?.$el?.contains(e.target)) {
          contextMenuVisible.value = false
        }
      },
      true,
    )
    if (props.contextMenuEnabled) {
      tableRef.value.$el.addEventListener("contextmenu", (e) => {
        e.preventDefault()
        handleContextMenu(null, null, e)
      })
    }
  })

  onUnmounted(() => {
    document.removeEventListener("click", handleClickOutside)
    document.removeEventListener("contextmenu", handleClickOutside)
  })

  const menuItemClick = (type) => {
    if (type === "add") {
      emit("menuClick", type)
    }
  }

  return {
    contextMenuVisible,
    virtualRef,
    contextMenuRowIndex,
    isRowContextMenu,
    handleContextMenu,
    closeContextMenu,
    menuItemClick,
    shouldShowMenuItem,
  }
}

// 行操作工具函数
const useRowOperations = () => {
  const createEmptyRow = () => {
    let emptyRow = {}

    if (props.initRow) {
      emptyRow = props.initRow()
    } else {
      props.columns.forEach((col) => {
        emptyRow[col.prop] = undefined
      })
    }

    return emptyRow
  }

  const handleAddRow = () => {
    const newData = [...props.data]
    newData.push(createEmptyRow())
    emit("update:data", newData)

    nextTick(() => {
      activeCell.value = [newData.length - 1, activeCell.value[1]]
    })
  }

  const handleInsertRow = (direction) => {
    const newData = [...props.data]
    const insertIndex = direction === "up" ? contextMenuRowIndex.value : contextMenuRowIndex.value + 1
    const cloneData = {
      ...newData[contextMenuRowIndex.value],
    }
    const insertData = createEmptyRow()
    newData.splice(insertIndex, 0, insertData)
    closeContextMenu()

    emit("update:data", newData)
    emit("menuClick", `insert_${direction}`, { rowIndex: insertIndex, data: insertData, cloneData })
    nextTick(() => {
      tableRef.value.$el.querySelectorAll(".custom-cell")[insertIndex * props.columns.length]?.focus()
    })
  }

  const handleDeleteRow = () => {
    const newData = [...props.data]
    newData.splice(contextMenuRowIndex.value, 1)
    emit("update:data", newData)
    emit("menuClick", "delete", { rowIndex: contextMenuRowIndex.value })
    closeContextMenu()
  }

  const handleChange = (row) => {
    emit("rowInputChange", row)
  }

  const rowClick = (row, column, event) => {
    const index = props.data.indexOf(row)

    rowIndex.value = index
    emit("rowClick", row, column, event)
  }

  const tableRowClassName = (rowCls) => {
    if (!props.highlight) {
      return ""
    }
    return rowCls.rowIndex === rowIndex.value ? "active-row" : ""
  }

  return {
    handleAddRow,
    handleInsertRow,
    handleDeleteRow,
    handleChange,
    rowClick,
    tableRowClassName,
  }
}

// 组合所有功能
const tableState = useTableState()
const rowOperations = useRowOperations()
const { handleAddRow, tableRowClassName, handleInsertRow, handleDeleteRow, handleChange, rowClick } = rowOperations
const { tableRef, inputRef, syncData } = tableState
const { activeCell, selectedCell, isActiveCellAt, isSelectedCellAt, handleKeydown, handleDblclick, handleBlur, getCellClass, handleCellClick, handleCellFocus, handleCellBlur } = useTableEdit({ tableState, rowOperations })
const { contextMenuVisible, virtualRef, contextMenuRowIndex, isRowContextMenu, handleContextMenu, closeContextMenu, menuItemClick, shouldShowMenuItem } = useContextMenu(tableRef)
</script>
