<template>
  <el-input :model-value="displayValue" v-bind="$attrs" @keydown="handleKeydown" @input="handleInput" @update:model-value="updateValue" @compositionstart="handleComposition" @compositionend="handleComposition">
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}"></slot>
    </template>
  </el-input>
</template>

<script setup>
import { ref, watch } from "vue"

const props = defineProps({
  decimalPlaces: {
    type: Number,
    default: 1, // 默认限制1位小数
  },
  positiveOnly: {
    type: Boolean,
    default: false, // 默认允许负数
  },
  integerOnly: {
    type: Boolean,
    default: false, // 默认允许小数
  },
})

const modelValue = defineModel("modelValue", {
  type: Number,
  default: 0,
})

// 用于显示的字符串值
const displayValue = ref(modelValue.value !== null && modelValue.value !== undefined ? String(modelValue.value) : "")

// 监听 modelValue 变化，更新显示值
watch(
  () => modelValue.value,
  (newVal) => {
    displayValue.value = newVal !== null && newVal !== undefined ? String(newVal) : ""
  },
)

// 是否正在输入中文
const isComposing = ref(false)

// 处理中文输入法事件
const handleComposition = (event) => {
  // 安全检查 event 是否存在
  if (!event) return

  isComposing.value = event.type === "compositionstart"

  // 如果输入法结束，且输入的是非法字符（中文、中文标点、英文字母），则清空输入
  if (event.type === "compositionend" && event.data && (/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(event.data) || /[a-zA-Z]/.test(event.data))) {
    // 记录当前光标位置
    const input = event.target
    const selectionStart = input?.selectionStart || 0

    // 获取当前值
    const currentValue = displayValue.value || ""

    // 计算光标前后的内容
    const beforeCursor = currentValue.substring(0, selectionStart)
    const afterCursor = currentValue.substring(selectionStart)

    // 过滤所有非法字符
    let beforeCursorFiltered = beforeCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")
    let afterCursorFiltered = afterCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")

    // 处理多个小数点：只保留第一个小数点
    const combinedFiltered = beforeCursorFiltered + afterCursorFiltered
    const firstDotIndex = combinedFiltered.indexOf(".")
    let newCursorPos

    if (firstDotIndex !== -1) {
      const beforeFirstDot = combinedFiltered.substring(0, firstDotIndex + 1)
      const afterFirstDot = combinedFiltered.substring(firstDotIndex + 1).replace(/\./g, "")
      const finalValue = beforeFirstDot + afterFirstDot

      // 重新计算光标位置
      newCursorPos = Math.min(beforeCursorFiltered.length, finalValue.length)
      displayValue.value = finalValue
    } else {
      // 没有小数点的情况
      newCursorPos = beforeCursorFiltered.length
      displayValue.value = beforeCursorFiltered + afterCursorFiltered
    }

    // 在下一个事件循环中恢复光标位置
    setTimeout(() => {
      if (input && input.setSelectionRange) {
        input.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)

    event.preventDefault()
  }
}

// 更新值并确保类型为数字
const updateValue = (val) => {
  // 如果正在输入中文，不处理更新
  if (isComposing.value) return

  // 安全检查 val 是否为有效值
  if (val === null || val === undefined) {
    modelValue.value = null
    displayValue.value = ""
    return
  }

  // 确保 val 是字符串
  val = String(val)

  // 记录当前光标位置
  const input = document.activeElement
  const selectionStart = input?.selectionStart || 0

  // 检查是否包含非法字符（中文字符、中文标点、英文字母）或多个小数点
  const hasIllegalChars = /[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(val) || /[a-zA-Z]/.test(val)
  const hasMultipleDots = (val.match(/\./g) || []).length > 1

  if (hasIllegalChars || hasMultipleDots) {
    // 过滤掉所有非法字符和多余的小数点
    const beforeCursor = val.substring(0, selectionStart)
    const afterCursor = val.substring(selectionStart)

    let beforeCursorFiltered = beforeCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")
    let afterCursorFiltered = afterCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")

    // 处理多个小数点：只保留第一个小数点
    const combinedFiltered = beforeCursorFiltered + afterCursorFiltered
    const firstDotIndex = combinedFiltered.indexOf(".")
    if (firstDotIndex !== -1) {
      // 移除第一个小数点之后的所有小数点
      const beforeFirstDot = combinedFiltered.substring(0, firstDotIndex + 1)
      const afterFirstDot = combinedFiltered.substring(firstDotIndex + 1).replace(/\./g, "")
      val = beforeFirstDot + afterFirstDot

      // 重新计算光标位置
      const newCursorPos = Math.min(beforeCursorFiltered.length, val.length)
      displayValue.value = val

      setTimeout(() => {
        if (input && input.setSelectionRange) {
          input.setSelectionRange(newCursorPos, newCursorPos)
        }
      }, 0)
    } else {
      // 没有小数点的情况
      val = beforeCursorFiltered + afterCursorFiltered
      const newCursorPos = beforeCursorFiltered.length
      displayValue.value = val

      setTimeout(() => {
        if (input && input.setSelectionRange) {
          input.setSelectionRange(newCursorPos, newCursorPos)
        }
      }, 0)
    }
  } else {
    displayValue.value = val
  }

  // 处理特殊情况
  if (val === "" || val === null || val === undefined) {
    modelValue.value = null
    return
  }

  // 处理只有负号的情况
  if (val === "-") {
    // 如果只允许正数，不允许输入负号
    if (props.positiveOnly) {
      displayValue.value = ""
      return
    }
    displayValue.value = "-"
    return // 不更新modelValue，等待用户输入数字
  }

  // 如果只输入了小数点，自动补0
  if (val === ".") {
    // 如果只允许整数，不允许输入小数点
    if (props.integerOnly) {
      displayValue.value = ""
      return
    }
    displayValue.value = "0."
    return // 不更新modelValue，等待用户输入小数部分
  }

  // 如果是负号加小数点，转为 -0.
  if (val === "-.") {
    // 如果只允许正数或只允许整数，不允许这种输入
    if (props.positiveOnly || props.integerOnly) {
      displayValue.value = ""
      return
    }
    displayValue.value = "-0."
    return // 不更新modelValue，等待用户输入小数部分
  }

  // 处理 -0 的情况，保持显示为 -0，等待用户继续输入
  if (val === "-0") {
    // 如果只允许正数，不允许负数
    if (props.positiveOnly) {
      displayValue.value = "0"
      return
    }
    displayValue.value = "-0"
    return // 不更新modelValue，等待用户输入小数点或其他数字
  }

  // 处理 -0. 的情况，等待用户输入小数部分
  if (val === "-0.") {
    // 如果只允许正数或只允许整数，不允许这种输入
    if (props.positiveOnly) {
      displayValue.value = "0"
      return
    }
    if (props.integerOnly) {
      displayValue.value = "0"
      return
    }
    displayValue.value = "-0."
    return // 不更新modelValue，等待用户输入小数部分
  }

  // 处理多个连续的0的情况，避免显示 000000 这样的值
  if (/^-?0+$/.test(val) && val.length > 1) {
    // 如果是多个0，只保留一个0
    displayValue.value = val.startsWith("-") ? "-0" : "0"
    return // 不更新modelValue，等待用户输入其他数字或小数点
  }

  // 处理以0开头但不是小数的情况，如 01, 02, 001 等
  if (/^-?0+\d+$/.test(val)) {
    // 移除前导0，但保留负号
    const cleanedVal = val.replace(/^(-?)0+/, "$1")
    displayValue.value = cleanedVal
    const numValue = parseFloat(cleanedVal)
    if (!isNaN(numValue) && numValue !== modelValue.value) {
      modelValue.value = numValue
    }
    return
  }

  // 检查是否为有效数字
  const numValue = parseFloat(val)
  if (!isNaN(numValue)) {
    // 检查配置限制
    if (props.positiveOnly && numValue < 0) {
      // 如果只允许正数但输入了负数，转换为正数
      const positiveValue = Math.abs(numValue)
      displayValue.value = String(positiveValue)
      if (positiveValue !== modelValue.value) {
        modelValue.value = positiveValue
      }
      return
    }

    if (props.integerOnly && !Number.isInteger(numValue)) {
      // 如果只允许整数但输入了小数，截取整数部分
      const integerValue = Math.floor(Math.abs(numValue)) * (numValue < 0 ? -1 : 1)
      displayValue.value = String(integerValue)
      if (integerValue !== modelValue.value) {
        modelValue.value = integerValue
      }
      return
    }

    // 只有当值确实变化时才更新，避免不必要的触发
    if (numValue !== modelValue.value) {
      modelValue.value = numValue
    }
  } else {
    // 如果不是有效数字，但是是正在输入中的状态（如 "-0."），保持显示值不变
    // 这种情况已经在上面的特殊情况中处理了
  }
}

// 处理 input 事件，作为最后的防线过滤非法字符
const handleInput = (e) => {
  // 安全检查 e.target 是否存在
  if (!e || !e.target) return

  const value = e.target.value

  // 检查 value 是否为有效字符串
  if (typeof value !== "string") return

  // 检查是否包含非法字符或多个小数点
  const hasIllegalChars = /[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(value)
  const hasMultipleDots = (value.match(/\./g) || []).length > 1

  if (hasIllegalChars || hasMultipleDots) {
    // 记录当前光标位置
    const selectionStart = e.target.selectionStart || 0

    // 过滤掉所有非法字符
    let filteredValue = value.replace(/[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g, "")

    // 处理多个小数点：只保留第一个小数点
    const firstDotIndex = filteredValue.indexOf(".")
    if (firstDotIndex !== -1) {
      const beforeFirstDot = filteredValue.substring(0, firstDotIndex + 1)
      const afterFirstDot = filteredValue.substring(firstDotIndex + 1).replace(/\./g, "")
      filteredValue = beforeFirstDot + afterFirstDot
    }

    // 计算新的光标位置
    const beforeCursor = value.substring(0, selectionStart)
    let filteredBeforeCursor = beforeCursor.replace(/[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g, "")

    // 调整光标位置以考虑多个小数点的移除
    const beforeCursorDotIndex = filteredBeforeCursor.indexOf(".")
    if (beforeCursorDotIndex !== -1) {
      const beforeDot = filteredBeforeCursor.substring(0, beforeCursorDotIndex + 1)
      const afterDot = filteredBeforeCursor.substring(beforeCursorDotIndex + 1).replace(/\./g, "")
      filteredBeforeCursor = beforeDot + afterDot
    }

    const newCursorPos = Math.min(filteredBeforeCursor.length, filteredValue.length)

    // 处理多个连续的0的情况
    if (/^-?0+$/.test(filteredValue) && filteredValue.length > 1) {
      filteredValue = filteredValue.startsWith("-") ? "-0" : "0"
      newCursorPos = Math.min(newCursorPos, filteredValue.length)
    }

    // 处理以0开头但不是小数的情况，如 01, 02, 001 等
    if (/^-?0+\d+$/.test(filteredValue)) {
      filteredValue = filteredValue.replace(/^(-?)0+/, "$1")
      newCursorPos = Math.min(newCursorPos, filteredValue.length)
    }

    // 更新值
    displayValue.value = filteredValue

    // 恢复光标位置
    setTimeout(() => {
      if (e.target && e.target.setSelectionRange) {
        e.target.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }
}

const handleKeydown = (e) => {
  // 安全检查 e 和 e.target 是否存在
  if (!e || !e.target) return

  // 允许的按键：负号、数字、小数点、删除、退格、方向键、Tab键
  const allowedKeys = ["-", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ".", "Delete", "Backspace", "ArrowLeft", "ArrowRight", "Tab", "Home", "End"]

  // 检查是否为字母（包括中英文字母）
  const isLetter = /^[a-zA-Z\u4e00-\u9fa5]$/.test(e.key)

  // 如果是字母，直接阻止
  if (isLetter) {
    e.preventDefault()
    return
  }

  // 如果不是允许的按键，则阻止输入
  if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
    e.preventDefault()
    return
  }

  // 处理负号
  if (e.key === "-") {
    // 如果只允许正数，阻止输入负号
    if (props.positiveOnly) {
      e.preventDefault()
      return
    }

    const value = e.target.value || ""
    const selectionStart = e.target.selectionStart || 0

    // 负号只能出现在开头
    if (selectionStart !== 0 || value.includes("-")) {
      e.preventDefault()
      return
    }
  }

  // 处理小数点
  if (e.key === ".") {
    // 如果只允许整数，阻止输入小数点
    if (props.integerOnly) {
      e.preventDefault()
      return
    }

    const value = e.target.value || ""

    // 如果已经有小数点，则阻止再次输入小数点
    if (value.includes(".")) {
      e.preventDefault()
      return
    }

    // 如果设置了不允许小数（decimalPlaces为0），则阻止输入小数点
    if (props.decimalPlaces === 0) {
      e.preventDefault()
      return
    }
  }

  // 处理小数位数限制
  if (/^\d$/.test(e.key)) {
    const value = e.target.value || ""
    const selectionStart = e.target.selectionStart || 0
    const selectionEnd = e.target.selectionEnd || 0

    // 检查是否在小数点后面输入
    if (value.includes(".")) {
      const parts = value.split(".")
      const cursorAfterDecimal = selectionStart > value.indexOf(".")

      // 如果光标在小数点后面，并且已经达到了限制的小数位数
      if (cursorAfterDecimal && parts[1] && parts[1].length >= props.decimalPlaces) {
        // 如果有选中文本，允许替换
        if (selectionStart !== selectionEnd) {
          // 检查替换后是否会超过小数位数
          const decimalPartSelected = selectionStart > value.indexOf(".") && selectionEnd > value.indexOf(".")
          if (decimalPartSelected) {
            // 计算替换后的小数部分长度
            const decimalStartPos = value.indexOf(".") + 1
            const selectedDecimalDigits = Math.min(selectionEnd, value.length) - Math.max(selectionStart, decimalStartPos)
            const remainingDecimalDigits = parts[1].length - selectedDecimalDigits

            // 如果替换后仍然超过限制，则阻止输入
            if (remainingDecimalDigits >= props.decimalPlaces) {
              e.preventDefault()
              return
            }
          }
        } else {
          // 没有选中文本，直接阻止输入
          e.preventDefault()
          return
        }
      }
    }
  }
}
</script>
